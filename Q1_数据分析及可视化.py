"""
12hz+48hz重采样 + 数据分析可视化
"""
import os
import numpy as np
import pandas as pd
import scipy.io as sio
from matplotlib import pyplot as plt
from matplotlib.colors import LinearSegmentedColormap
import seaborn as sns
from scipy import signal
from scipy.fft import fft, fftfreq
from scipy.signal import welch, hilbert, resample_poly
import pywt
from sklearn.preprocessing import StandardScaler
import pickle
import warnings
import scipy.stats

warnings.filterwarnings('ignore')
plt.rcParams['font.sans-serif'] = ['PingFang SC', 'SimHei', 'STHeiti', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False

class BearingDataProcessor:
    def __init__(self, source_data_path):
        self.source_data_path = source_data_path
        self.bearing_params = {
            'SKF6205': {'n': 9, 'd': 0.3126, 'D': 1.537},
            'SKF6203': {'n': 9, 'd': 0.2656, 'D': 1.122}
        }
        self.selected_files = []
        self.features_data = []
        self.window_size = 4096
        self.overlap_ratio = 0.5
        self.target_fs = 32000  # 目标采样率，接近目标域的32kHz
        self.output_dir = os.path.join(os.getcwd(), "图片")

    def get_fault_frequencies(self, rpm, bearing_type='SKF6205'):
        """计算轴承故障特征频率"""
        params = self.bearing_params[bearing_type]
        fr = rpm / 60
        bpfo = fr * (params['n'] / 2) * (1 - params['d'] / params['D'])
        bpfi = fr * (params['n'] / 2) * (1 + params['d'] / params['D'])
        bsf = fr * (params['D'] / params['d']) * (1 - (params['d'] / params['D']) ** 2)
        return {'BPFO': bpfo, 'BPFI': bpfi, 'BSF': bsf, 'FR': fr}

    def save_figure(self, fig, filename):
        """Save Matplotlib figure to output directory."""
        os.makedirs(self.output_dir, exist_ok=True)
        filepath = os.path.join(self.output_dir, filename)
        fig.savefig(filepath, bbox_inches='tight')
        plt.close(fig)
        print(f"图像已保存: {filepath}")

    def resample_signal(self, signal_data, original_fs, target_fs):
        """重采样信号到目标采样率"""
        if original_fs == target_fs:
            return signal_data

        # 计算重采样比例
        if original_fs > target_fs:
            # 降采样
            up = target_fs
            down = original_fs
            # 简化分数
            gcd = np.gcd(up, down)
            up = up // gcd
            down = down // gcd
        else:
            # 升采样
            up = target_fs
            down = original_fs
            gcd = np.gcd(up, down)
            up = up // gcd
            down = down // gcd

        # 使用scipy的resample_poly进行重采样
        resampled = resample_poly(signal_data, up, down)
        return resampled

    def select_source_data(self):
        """筛选源域数据，包含12kHz和48kHz数据"""
        selected_files = {
            'Normal': [],
            'OR': [],
            'IR': [],
            'B': []
        }

        # 正常状态：48kHz数据（全部4个文件）
        normal_files_48k = [
            {'path': '48kHz_Normal_data/N_0.mat', 'fs': 48000},
            {'path': '48kHz_Normal_data/N_1_(1772rpm).mat', 'fs': 48000},
            {'path': '48kHz_Normal_data/N_2_(1750rpm).mat', 'fs': 48000},
            {'path': '48kHz_Normal_data/N_3.mat', 'fs': 48000}
        ]
        selected_files['Normal'] = normal_files_48k

        # 外圈故障：混合12kHz和48kHz数据（全部文件）
        or_files = []

        # 48kHz外圈故障数据（全部可用文件）
        or_48k_paths = [
            # Centered位置
            '48kHz_DE_data/OR/Centered/0007/OR007@6_0.mat',
            '48kHz_DE_data/OR/Centered/0007/OR007@6_1.mat',
            '48kHz_DE_data/OR/Centered/0007/OR007@6_2.mat',
            '48kHz_DE_data/OR/Centered/0007/OR007@6_3.mat',
            '48kHz_DE_data/OR/Centered/0014/OR014@6_0.mat',
            '48kHz_DE_data/OR/Centered/0014/OR014@6_1.mat',
            '48kHz_DE_data/OR/Centered/0014/OR014@6_2.mat',
            '48kHz_DE_data/OR/Centered/0014/OR014@6_3.mat',
            '48kHz_DE_data/OR/Centered/0021/OR021@6_0.mat',
            '48kHz_DE_data/OR/Centered/0021/OR021@6_1.mat',
            '48kHz_DE_data/OR/Centered/0021/OR021@6_2.mat',
            '48kHz_DE_data/OR/Centered/0021/OR021@6_3.mat',
            # Opposite位置
            '48kHz_DE_data/OR/Opposite/0007/OR007@12_0.mat',
            '48kHz_DE_data/OR/Opposite/0007/OR007@12_1.mat',
            '48kHz_DE_data/OR/Opposite/0007/OR007@12_2.mat',
            '48kHz_DE_data/OR/Opposite/0007/OR007@12_3.mat',
            '48kHz_DE_data/OR/Opposite/0021/OR021@12_0.mat',
            '48kHz_DE_data/OR/Opposite/0021/OR021@12_1.mat',
            '48kHz_DE_data/OR/Opposite/0021/OR021@12_2.mat',
            '48kHz_DE_data/OR/Opposite/0021/OR021@12_3.mat',
            # Orthogonal位置
            '48kHz_DE_data/OR/Orthogonal/0007/OR007@3_0.mat',
            '48kHz_DE_data/OR/Orthogonal/0007/OR007@3_1.mat',
            '48kHz_DE_data/OR/Orthogonal/0007/OR007@3_2.mat',
            '48kHz_DE_data/OR/Orthogonal/0007/OR007@3_3.mat',
            '48kHz_DE_data/OR/Orthogonal/0021/OR021@3_0.mat',
            '48kHz_DE_data/OR/Orthogonal/0021/OR021@3_1.mat',
            '48kHz_DE_data/OR/Orthogonal/0021/OR021@3_2.mat',
            '48kHz_DE_data/OR/Orthogonal/0021/OR021@3_3.mat'
        ]
        for path in or_48k_paths:
            or_files.append({'path': path, 'fs': 48000})

        # 12kHz外圈故障数据（全部可用文件）
        or_12k_paths = [
            # Centered位置
            '12kHz_DE_data/OR/Centered/0007/OR007@6_0.mat',
            '12kHz_DE_data/OR/Centered/0007/OR007@6_1.mat',
            '12kHz_DE_data/OR/Centered/0007/OR007@6_2.mat',
            '12kHz_DE_data/OR/Centered/0007/OR007@6_3.mat',
            '12kHz_DE_data/OR/Centered/0014/OR014@6_0.mat',
            '12kHz_DE_data/OR/Centered/0014/OR014@6_1.mat',
            '12kHz_DE_data/OR/Centered/0014/OR014@6_2.mat',
            '12kHz_DE_data/OR/Centered/0014/OR014@6_3.mat',
            '12kHz_DE_data/OR/Centered/0021/OR021@6_0.mat',
            '12kHz_DE_data/OR/Centered/0021/OR021@6_1.mat',
            '12kHz_DE_data/OR/Centered/0021/OR021@6_2.mat',
            '12kHz_DE_data/OR/Centered/0021/OR021@6_3.mat',
            # Opposite位置
            '12kHz_DE_data/OR/Opposite/0007/OR007@12_0.mat',
            '12kHz_DE_data/OR/Opposite/0007/OR007@12_1.mat',
            '12kHz_DE_data/OR/Opposite/0007/OR007@12_2.mat',
            '12kHz_DE_data/OR/Opposite/0007/OR007@12_3.mat',
            '12kHz_DE_data/OR/Opposite/0021/OR021@12_0.mat',
            '12kHz_DE_data/OR/Opposite/0021/OR021@12_1.mat',
            '12kHz_DE_data/OR/Opposite/0021/OR021@12_2.mat',
            '12kHz_DE_data/OR/Opposite/0021/OR021@12_3.mat',
            # Orthogonal位置
            '12kHz_DE_data/OR/Orthogonal/0007/OR007@3_0.mat',
            '12kHz_DE_data/OR/Orthogonal/0007/OR007@3_1.mat',
            '12kHz_DE_data/OR/Orthogonal/0007/OR007@3_2.mat',
            '12kHz_DE_data/OR/Orthogonal/0007/OR007@3_3.mat',
            '12kHz_DE_data/OR/Orthogonal/0021/OR021@3_0.mat',
            '12kHz_DE_data/OR/Orthogonal/0021/OR021@3_1.mat',
            '12kHz_DE_data/OR/Orthogonal/0021/OR021@3_2.mat',
            '12kHz_DE_data/OR/Orthogonal/0021/OR021@3_3.mat'
        ]
        for path in or_12k_paths:
            or_files.append({'path': path, 'fs': 12000})

        selected_files['OR'] = or_files

        # 内圈故障：混合采样率（全部文件）
        ir_files = []

        # 48kHz内圈故障数据（全部可用文件）
        ir_48k_paths = [
            '48kHz_DE_data/IR/0007/IR007_0.mat',
            '48kHz_DE_data/IR/0007/IR007_1.mat',
            '48kHz_DE_data/IR/0007/IR007_2.mat',
            '48kHz_DE_data/IR/0007/IR007_3.mat',
            '48kHz_DE_data/IR/0014/IR014_0.mat',
            '48kHz_DE_data/IR/0014/IR014_1.mat',
            '48kHz_DE_data/IR/0014/IR014_2.mat',
            '48kHz_DE_data/IR/0014/IR014_3.mat',
            '48kHz_DE_data/IR/0021/IR021_0.mat',
            '48kHz_DE_data/IR/0021/IR021_1.mat',
            '48kHz_DE_data/IR/0021/IR021_2.mat',
            '48kHz_DE_data/IR/0021/IR021_3.mat'
        ]
        for path in ir_48k_paths:
            ir_files.append({'path': path, 'fs': 48000})

        # 12kHz内圈故障数据（全部可用文件）
        ir_12k_paths = [
            '12kHz_DE_data/IR/0007/IR007_0.mat',
            '12kHz_DE_data/IR/0007/IR007_1.mat',
            '12kHz_DE_data/IR/0007/IR007_2.mat',
            '12kHz_DE_data/IR/0007/IR007_3.mat',
            '12kHz_DE_data/IR/0014/IR014_0.mat',
            '12kHz_DE_data/IR/0014/IR014_1.mat',
            '12kHz_DE_data/IR/0014/IR014_2.mat',
            '12kHz_DE_data/IR/0014/IR014_3.mat',
            '12kHz_DE_data/IR/0021/IR021_0.mat',
            '12kHz_DE_data/IR/0021/IR021_1.mat',
            '12kHz_DE_data/IR/0021/IR021_2.mat',
            '12kHz_DE_data/IR/0021/IR021_3.mat',
            '12kHz_DE_data/IR/0028/IR028_0_(1797rpm).mat',
            '12kHz_DE_data/IR/0028/IR028_1_(1772rpm).mat',
            '12kHz_DE_data/IR/0028/IR028_2_(1750rpm).mat',
            '12kHz_DE_data/IR/0028/IR028_3_(1730rpm).mat'
        ]
        for path in ir_12k_paths:
            ir_files.append({'path': path, 'fs': 12000})

        selected_files['IR'] = ir_files

        # 滚动体故障：混合采样率（全部文件）
        b_files = []

        # 48kHz滚动体故障数据（全部可用文件）
        b_48k_paths = [
            '48kHz_DE_data/B/0007/B007_0.mat',
            '48kHz_DE_data/B/0007/B007_1.mat',
            '48kHz_DE_data/B/0007/B007_2.mat',
            '48kHz_DE_data/B/0007/B007_3.mat',
            '48kHz_DE_data/B/0014/B014_0.mat',
            '48kHz_DE_data/B/0014/B014_1.mat',
            '48kHz_DE_data/B/0014/B014_2.mat',
            '48kHz_DE_data/B/0014/B014_3.mat',
            '48kHz_DE_data/B/0021/B021_0.mat',
            '48kHz_DE_data/B/0021/B021_1.mat',
            '48kHz_DE_data/B/0021/B021_2.mat',
            '48kHz_DE_data/B/0021/B021_3.mat'
        ]
        for path in b_48k_paths:
            b_files.append({'path': path, 'fs': 48000})

        # 12kHz滚动体故障数据（全部可用文件）
        b_12k_paths = [
            '12kHz_DE_data/B/0007/B007_0.mat',
            '12kHz_DE_data/B/0007/B007_1.mat',
            '12kHz_DE_data/B/0007/B007_2.mat',
            '12kHz_DE_data/B/0007/B007_3.mat',
            '12kHz_DE_data/B/0014/B014_0.mat',
            '12kHz_DE_data/B/0014/B014_1.mat',
            '12kHz_DE_data/B/0014/B014_2.mat',
            '12kHz_DE_data/B/0014/B014_3.mat',
            '12kHz_DE_data/B/0021/B021_0.mat',
            '12kHz_DE_data/B/0021/B021_1.mat',
            '12kHz_DE_data/B/0021/B021_2.mat',
            '12kHz_DE_data/B/0021/B021_3.mat',
            '12kHz_DE_data/B/0028/B028_0_(1797rpm).mat',
            '12kHz_DE_data/B/0028/B028_1_(1772rpm).mat',
            '12kHz_DE_data/B/0028/B028_2_(1750rpm).mat',
            '12kHz_DE_data/B/0028/B028_3_(1730rpm).mat'
        ]
        for path in b_12k_paths:
            b_files.append({'path': path, 'fs': 12000})

        selected_files['B'] = b_files

        self.selected_files = selected_files

        # 打印筛选结果
        print("=" * 60)
        print("源域数据筛选结果（混合采样率）")
        print("=" * 60)
        for fault_type, files in selected_files.items():
            print(f"\n{fault_type}: {len(files)} 个文件")
            fs_48k = sum(1 for f in files if f['fs'] == 48000)
            fs_12k = sum(1 for f in files if f['fs'] == 12000)
            print(f"  - 48kHz: {fs_48k} 个文件")
            print(f"  - 12kHz: {fs_12k} 个文件")

        total_files = sum(len(files) for files in selected_files.values())
        print(f"\n总计: {total_files} 个文件")

        return selected_files

    def extract_samples_from_signal(self, signal_data, window_size=None, overlap_ratio=None):
        """将长信号分割成多个样本窗口"""
        if window_size is None:
            window_size = self.window_size
        if overlap_ratio is None:
            overlap_ratio = self.overlap_ratio

        step_size = int(window_size * (1 - overlap_ratio))
        samples = []

        for start in range(0, len(signal_data) - window_size + 1, step_size):
            window = signal_data[start:start + window_size]
            samples.append(window)

        return samples

    def check_signal_quality(self, signal_data):
        """检查信号质量"""
        if np.any(np.isnan(signal_data)) or np.any(np.isinf(signal_data)):
            return False

        signal_std = np.std(signal_data)
        if signal_std < 1e-8:
            return False

        signal_max = np.max(np.abs(signal_data))
        if signal_max > 100 * signal_std:
            return False

        return True

    def load_mat_file(self, file_path):
        """加载MAT文件"""
        full_path = os.path.join(self.source_data_path, file_path)
        try:
            mat_data = sio.loadmat(full_path)

            de_data, fe_data, ba_data, rpm_data = None, None, None, None

            for key in mat_data.keys():
                if not key.startswith('__'):
                    if 'DE_time' in key:
                        de_data = mat_data[key].flatten()
                    elif 'FE_time' in key:
                        fe_data = mat_data[key].flatten()
                    elif 'BA_time' in key:
                        ba_data = mat_data[key].flatten()
                    elif 'RPM' in key:
                        rpm_data = mat_data[key].flatten()[0] if len(mat_data[key].flatten()) > 0 else 1797

            return {
                'DE': de_data,
                'FE': fe_data,
                'BA': ba_data,
                'RPM': rpm_data if rpm_data is not None else 1797
            }
        except Exception as e:
            print(f"加载文件失败 {file_path}: {e}")
            return None

    def extract_time_domain_features(self, signal_data):
        """提取时域特征"""
        features = {}

        # 基本统计特征
        features['mean'] = np.mean(signal_data)
        features['std'] = np.std(signal_data)
        features['var'] = np.var(signal_data)
        features['rms'] = np.sqrt(np.mean(signal_data ** 2))
        features['peak'] = np.max(np.abs(signal_data))
        features['peak_to_peak'] = np.ptp(signal_data)

        # 高阶统计特征
        features['skewness'] = scipy.stats.skew(signal_data)
        features['kurtosis'] = scipy.stats.kurtosis(signal_data)

        # 形态特征
        mean_abs = np.mean(np.abs(signal_data))
        if mean_abs > 0:
            features['crest_factor'] = features['peak'] / features['rms'] if features['rms'] > 0 else 0
            features['impulse_factor'] = features['peak'] / mean_abs
            features['shape_factor'] = features['rms'] / mean_abs if mean_abs > 0 else 0
            sqrt_mean_sqrt = np.mean(np.sqrt(np.abs(signal_data)))
            features['clearance_factor'] = features['peak'] / (sqrt_mean_sqrt ** 2) if sqrt_mean_sqrt > 0 else 0
        else:
            features['crest_factor'] = 0
            features['impulse_factor'] = 0
            features['shape_factor'] = 0
            features['clearance_factor'] = 0

        return features

    def extract_frequency_domain_features(self, signal_data, fs, rpm):
        """提取频域特征（修复版本）"""
        features = {}

        # FFT变换
        N = len(signal_data)
        fft_vals = fft(signal_data)
        freqs = fftfreq(N, 1 / fs)[:N // 2]
        magnitude = np.abs(fft_vals[:N // 2])

        # 防止除零错误
        total_magnitude = np.sum(magnitude)
        if total_magnitude == 0:
            return {
                'spectral_centroid': 0, 'spectral_variance': 0, 'spectral_skewness': 0, 'spectral_kurtosis': 0,
                'BPFO_amplitude': 0, 'BPFI_amplitude': 0, 'BSF_amplitude': 0, 'FR_amplitude': 0,
                'low_freq_energy_ratio': 0, 'mid_freq_energy_ratio': 0, 'high_freq_energy_ratio': 0
            }

        # 频谱特征
        features['spectral_centroid'] = np.sum(freqs * magnitude) / total_magnitude
        spectral_variance = np.sum(((freqs - features['spectral_centroid']) ** 2) * magnitude) / total_magnitude
        features['spectral_variance'] = spectral_variance

        if spectral_variance > 0:
            features['spectral_skewness'] = np.sum(((freqs - features['spectral_centroid']) ** 3) * magnitude) / (
                    total_magnitude * spectral_variance ** 1.5)
            features['spectral_kurtosis'] = np.sum(((freqs - features['spectral_centroid']) ** 4) * magnitude) / (
                    total_magnitude * spectral_variance ** 2)
        else:
            features['spectral_skewness'] = 0
            features['spectral_kurtosis'] = 0

        # 改进的故障特征频率分析
        try:
            fault_freqs = self.get_fault_frequencies(rpm)

            # 频率分辨率
            freq_resolution = fs / N

            # 为每个故障类型计算幅值
            for fault_type, freq in fault_freqs.items():
                if freq > 0 and freq < fs / 2:  # 确保频率在有效范围内
                    # 找到最接近的频率索引
                    freq_idx = np.argmin(np.abs(freqs - freq))

                    # 计算搜索范围：使用更大的搜索窗口
                    # 至少搜索±5Hz或±10个频率点，取较大值
                    freq_range_hz = max(5.0, freq * 0.05)  # 频率的5%或5Hz，取较大值
                    freq_range_points = max(10, int(freq_range_hz / freq_resolution))

                    # 确定搜索范围的索引
                    start_idx = max(0, freq_idx - freq_range_points // 2)
                    end_idx = min(len(magnitude), freq_idx + freq_range_points // 2 + 1)

                    if end_idx > start_idx:
                        # 在搜索范围内寻找最大值（故障特征通常表现为峰值）
                        search_magnitudes = magnitude[start_idx:end_idx]
                        max_amplitude = np.max(search_magnitudes)
                        mean_amplitude = np.mean(search_magnitudes)

                        # 使用最大值作为故障特征幅值（更能反映故障特征）
                        features[f'{fault_type}_amplitude'] = max_amplitude


                    else:
                        features[f'{fault_type}_amplitude'] = 0

                else:
                    # 频率超出范围时设为0
                    features[f'{fault_type}_amplitude'] = 0

        except Exception as e:
            # 如果故障频率计算失败，设置默认值
            print(f"故障特征频率计算失败: {e}, RPM: {rpm}")
            features['BPFO_amplitude'] = 0
            features['BPFI_amplitude'] = 0
            features['BSF_amplitude'] = 0
            features['FR_amplitude'] = 0

        # 频带能量比
        total_energy = np.sum(magnitude ** 2)
        if total_energy > 0:
            # 低频段 (0-500Hz)
            low_freq_idx = np.where(freqs <= 500)[0]
            features['low_freq_energy_ratio'] = np.sum(magnitude[low_freq_idx] ** 2) / total_energy if len(
                low_freq_idx) > 0 else 0

            # 中频段 (500-5000Hz)
            mid_freq_idx = np.where((freqs > 500) & (freqs <= 5000))[0]
            features['mid_freq_energy_ratio'] = np.sum(magnitude[mid_freq_idx] ** 2) / total_energy if len(
                mid_freq_idx) > 0 else 0

            # 高频段 (5000Hz以上)
            high_freq_idx = np.where(freqs > 5000)[0]
            features['high_freq_energy_ratio'] = np.sum(magnitude[high_freq_idx] ** 2) / total_energy if len(
                high_freq_idx) > 0 else 0
        else:
            features['low_freq_energy_ratio'] = 0
            features['mid_freq_energy_ratio'] = 0
            features['high_freq_energy_ratio'] = 0

        return features

    def extract_time_frequency_features(self, signal_data, fs):
        """提取时频域特征"""
        features = {}

        try:
            # 小波包分解
            wavelet = 'db4'
            levels = 4
            wp = pywt.WaveletPacket(signal_data, wavelet, maxlevel=levels)

            # 提取各频带能量
            energy_features = []
            for i in range(2 ** levels):
                try:
                    node_name = [node.path for node in wp.get_level(levels, 'freq')][i]
                    coeffs = wp[node_name].data
                    energy = np.sum(coeffs ** 2)
                    energy_features.append(energy)
                except:
                    energy_features.append(0)

            total_energy = sum(energy_features)
            if total_energy > 0:
                for i, energy in enumerate(energy_features):
                    features[f'wavelet_energy_band_{i}'] = energy / total_energy

                # 小波包熵
                energy_ratios = np.array(energy_features) / total_energy
                energy_ratios = energy_ratios[energy_ratios > 0]
                if len(energy_ratios) > 0:
                    features['wavelet_entropy'] = -np.sum(energy_ratios * np.log2(energy_ratios + 1e-12))
                else:
                    features['wavelet_entropy'] = 0
            else:
                for i in range(2 ** levels):
                    features[f'wavelet_energy_band_{i}'] = 0
                features['wavelet_entropy'] = 0

            # 包络分析
            analytic_signal = hilbert(signal_data)
            envelope = np.abs(analytic_signal)

            # 包络统计特征
            features['envelope_mean'] = np.mean(envelope)
            features['envelope_std'] = np.std(envelope)
            features['envelope_skewness'] = scipy.stats.skew(envelope)
            features['envelope_kurtosis'] = scipy.stats.kurtosis(envelope)

        except Exception as e:
            print(f"时频特征提取失败: {e}")
            for i in range(16):
                features[f'wavelet_energy_band_{i}'] = 0
            features['wavelet_entropy'] = 0
            features['envelope_mean'] = 0
            features['envelope_std'] = 0
            features['envelope_skewness'] = 0
            features['envelope_kurtosis'] = 0

        return features

    def extract_features(self, signal_data, sensor_type, fs, rpm, file_info):
        """提取完整特征集"""
        features = {}

        # 添加文件信息
        features['file_path'] = file_info['file_path']
        features['fault_type'] = file_info['fault_type']
        features['sensor_type'] = sensor_type
        features['rpm'] = rpm
        features['original_fs'] = file_info['original_fs']
        features['resampled_fs'] = fs

        # 数据预处理
        signal_data = signal_data - np.mean(signal_data)  # 去均值

        # 时域特征
        time_features = self.extract_time_domain_features(signal_data)
        for key, value in time_features.items():
            features[f'{sensor_type}_{key}'] = value

        # 频域特征
        freq_features = self.extract_frequency_domain_features(signal_data, fs, rpm)
        for key, value in freq_features.items():
            features[f'{sensor_type}_{key}'] = value

        # 时频域特征
        time_freq_features = self.extract_time_frequency_features(signal_data, fs)
        for key, value in time_freq_features.items():
            features[f'{sensor_type}_{key}'] = value

        return features

    def process_all_selected_files(self):
        """处理所有选定的文件（包含重采样）"""
        print("\n" + "=" * 60)
        print("开始特征提取（混合采样率 + 重采样）")
        print("=" * 60)
        print(f"目标采样率: {self.target_fs} Hz")
        print(f"窗口参数: 窗口大小={self.window_size}, 重叠率={self.overlap_ratio}")

        all_features = []
        total_samples = 0

        for fault_type, file_list in self.selected_files.items():
            print(f"\n处理 {fault_type} 故障类型...")
            fault_samples = 0

            for file_info in file_list:
                file_path = file_info['path']
                original_fs = file_info['fs']
                print(f"  处理文件: {file_path} (原始采样率: {original_fs} Hz)")

                # 加载文件
                mat_data = self.load_mat_file(file_path)
                if mat_data is None:
                    continue

                rpm = mat_data['RPM']

                # 处理DE数据（主要传感器）
                if mat_data['DE'] is not None:
                    signal_length = len(mat_data['DE'])
                    print(f"    原始信号长度: {signal_length} 点 ({signal_length / original_fs:.2f} 秒)")

                    # 重采样到目标采样率
                    resampled_signal = self.resample_signal(mat_data['DE'], original_fs, self.target_fs)
                    print(
                        f"    重采样后长度: {len(resampled_signal)} 点 ({len(resampled_signal) / self.target_fs:.2f} 秒)")

                    # 分割信号为多个样本
                    samples = self.extract_samples_from_signal(resampled_signal)
                    valid_samples = 0

                    for i, sample in enumerate(samples):
                        # 检查样本质量
                        if not self.check_signal_quality(sample):
                            continue

                        file_info_dict = {
                            'file_path': f"{file_path}_sample_{i}",
                            'fault_type': fault_type,
                            'original_fs': original_fs
                        }

                        # 提取特征
                        features = self.extract_features(sample, 'DE', self.target_fs, rpm, file_info_dict)
                        all_features.append(features)
                        valid_samples += 1

                    print(f"    从DE信号中提取了 {valid_samples} 个有效样本")
                    fault_samples += valid_samples

            print(f"  {fault_type} 总样本数: {fault_samples}")
            total_samples += fault_samples

        self.features_data = all_features
        print(f"\n特征提取完成:")
        print(f"  总样本数: {len(all_features)}")

        # 打印各类别样本统计
        if all_features:
            df_temp = pd.DataFrame(all_features)
            print(f"\n各类别样本数:")
            for fault_type in ['Normal', 'OR', 'IR', 'B']:
                count = len(df_temp[df_temp['fault_type'] == fault_type])
                print(f"    {fault_type}: {count}")

            # 打印原始采样率分布
            print(f"\n原始采样率分布:")
            fs_counts = df_temp['original_fs'].value_counts()
            for fs, count in fs_counts.items():
                print(f"    {fs} Hz: {count}")

        return all_features

    def detect_outliers_by_category(self, df, method='iqr', iqr_factor=1.5, z_threshold=3):
        """
        分类别检测异常值

        Parameters:
        df: DataFrame - 包含特征数据的DataFrame
        method: str - 异常值检测方法 ('iqr', 'zscore', 'both')
        iqr_factor: float - IQR方法的倍数因子
        z_threshold: float - Z-score方法的阈值

        Returns:
        dict: 每个类别的异常值索引
        """
        outlier_indices = {}

        # 获取数值特征列（排除非数值列）
        numeric_cols = df.select_dtypes(include=[np.number]).columns.tolist()
        exclude_cols = ['rpm', 'original_fs', 'resampled_fs']  # 排除不需要检测异常值的列
        feature_cols = [col for col in numeric_cols if col not in exclude_cols]

        print(f"用于异常值检测的特征列数: {len(feature_cols)}")

        for fault_type in ['Normal', 'OR', 'IR', 'B']:
            category_data = df[df['fault_type'] == fault_type]
            if category_data.empty:
                outlier_indices[fault_type] = []
                continue

            print(f"\n检测 {fault_type} 类别异常值 (样本数: {len(category_data)})...")

            category_outliers = set()

            # 提取特征数据
            feature_data = category_data[feature_cols]

            if method == 'iqr' or method == 'both':
                # IQR方法检测异常值
                for col in feature_cols:
                    col_data = feature_data[col]
                    Q1 = col_data.quantile(0.25)
                    Q3 = col_data.quantile(0.75)
                    IQR = Q3 - Q1

                    if IQR > 0:  # 避免除零错误
                        lower_bound = Q1 - iqr_factor * IQR
                        upper_bound = Q3 + iqr_factor * IQR

                        # 找出异常值的索引
                        outliers = col_data[(col_data < lower_bound) | (col_data > upper_bound)].index
                        category_outliers.update(outliers)

            if method == 'zscore' or method == 'both':
                # Z-score方法检测异常值
                from scipy import stats
                z_scores = np.abs(stats.zscore(feature_data))
                outlier_mask = (z_scores > z_threshold).any(axis=1)
                z_outliers = feature_data[outlier_mask].index
                category_outliers.update(z_outliers)

            outlier_indices[fault_type] = list(category_outliers)
            print(
                f"  检测到 {len(category_outliers)} 个异常值 ({len(category_outliers) / len(category_data) * 100:.2f}%)")

        return outlier_indices

    def remove_outliers(self, method='iqr', iqr_factor=1.5, z_threshold=3, max_removal_ratio=0.1):
        """
        分类别移除异常值

        Parameters:
        method: str - 异常值检测方法 ('iqr', 'zscore', 'both')
        iqr_factor: float - IQR方法的倍数因子
        z_threshold: float - Z-score方法的阈值
        max_removal_ratio: float - 每个类别最大移除比例
        """
        if not self.features_data:
            print("没有特征数据可处理")
            return

        print("=" * 60)
        print("分类别异常值检测与移除")
        print("=" * 60)
        print(f"检测方法: {method}")
        if method == 'iqr' or method == 'both':
            print(f"IQR倍数因子: {iqr_factor}")
        if method == 'zscore' or method == 'both':
            print(f"Z-score阈值: {z_threshold}")
        print(f"每类最大移除比例: {max_removal_ratio * 100:.1f}%")

        # 转换为DataFrame
        df = pd.DataFrame(self.features_data)
        original_counts = df['fault_type'].value_counts()

        print(f"\n原始数据分布:")
        for fault_type in ['Normal', 'OR', 'IR', 'B']:
            count = original_counts.get(fault_type, 0)
            print(f"  {fault_type}: {count}")

        # 检测异常值
        outlier_indices = self.detect_outliers_by_category(df, method, iqr_factor, z_threshold)

        # 应用移除比例限制并移除异常值
        indices_to_remove = []
        removal_summary = {}

        for fault_type in ['Normal', 'OR', 'IR', 'B']:
            outliers = outlier_indices[fault_type]
            original_count = original_counts.get(fault_type, 0)

            if original_count == 0:
                removal_summary[fault_type] = {'removed': 0, 'remaining': 0, 'removal_ratio': 0}
                continue

            # 计算允许移除的最大数量
            max_removal = int(original_count * max_removal_ratio)

            # 如果异常值太多，随机选择一部分移除
            if len(outliers) > max_removal:
                print(f"  {fault_type}: 检测到{len(outliers)}个异常值，限制移除{max_removal}个")
                outliers = np.random.choice(outliers, max_removal, replace=False).tolist()

            indices_to_remove.extend(outliers)
            remaining_count = original_count - len(outliers)
            removal_ratio = len(outliers) / original_count

            removal_summary[fault_type] = {
                'removed': len(outliers),
                'remaining': remaining_count,
                'removal_ratio': removal_ratio
            }

        # 移除异常值
        df_cleaned = df.drop(indices_to_remove).reset_index(drop=True)

        # 更新特征数据
        self.features_data = df_cleaned.to_dict('records')

        # 打印移除结果
        print(f"\n异常值移除结果:")
        print(f"{'类别':<8} {'原始':<8} {'移除':<8} {'剩余':<8} {'移除率':<8}")
        print("-" * 50)

        for fault_type in ['Normal', 'OR', 'IR', 'B']:
            summary = removal_summary[fault_type]
            original = original_counts.get(fault_type, 0)
            print(
                f"{fault_type:<8} {original:<8} {summary['removed']:<8} {summary['remaining']:<8} {summary['removal_ratio'] * 100:<7.1f}%")

        total_original = original_counts.sum()
        total_removed = sum(summary['removed'] for summary in removal_summary.values())
        total_remaining = len(df_cleaned)
        overall_removal_ratio = total_removed / total_original if total_original > 0 else 0

        print("-" * 50)
        print(
            f"{'总计':<8} {total_original:<8} {total_removed:<8} {total_remaining:<8} {overall_removal_ratio * 100:<7.1f}%")

        print(f"\n数据清理完成:")
        print(f"  原始样本数: {total_original}")
        print(f"  移除样本数: {total_removed}")
        print(f"  保留样本数: {total_remaining}")
        print(f"  整体移除率: {overall_removal_ratio * 100:.2f}%")

    def analyze_basic_statistics(self):
        """基本统计分析"""
        print("\n" + "=" * 60)
        print("基本统计分析")
        print("=" * 60)

        if not self.features_data:
            print("没有可分析的数据，请先运行特征提取")
            return

        df = pd.DataFrame(self.features_data)

        exclude_cols = ['file_path', 'fault_type', 'sensor_type', 'rpm', 'original_fs', 'resampled_fs']
        feature_cols = [col for col in df.columns if col not in exclude_cols]

        print(f"数据概况:")
        print(f"  总样本数: {len(df)}")
        print(f"  特征维度: {len(feature_cols)}")

        print(f"\n故障类型分布:")
        fault_counts = df['fault_type'].value_counts()
        for fault_type, count in fault_counts.items():
            percentage = count / len(df) * 100
            print(f"  {fault_type}: {count} ({percentage:.1f}%)")

        print(f"\n原始采样率分布:")
        fs_counts = df['original_fs'].value_counts()
        for fs, count in fs_counts.items():
            percentage = count / len(df) * 100
            print(f"  {fs} Hz: {count} ({percentage:.1f}%)")

        feature_data = df[feature_cols]
        stats = feature_data.describe()

        print(f"\n特征统计概要:")
        print(f"  平均值范围: [{stats.loc['mean'].min():.4f}, {stats.loc['mean'].max():.4f}]")
        print(f"  标准差范围: [{stats.loc['std'].min():.4f}, {stats.loc['std'].max():.4f}]")
        print(f"  最小值范围: [{stats.loc['min'].min():.4f}, {stats.loc['min'].max():.4f}]")
        print(f"  最大值范围: [{stats.loc['max'].min():.4f}, {stats.loc['max'].max():.4f}]")

        return {
            'basic_stats': stats,
            'fault_counts': fault_counts,
            'fs_counts': fs_counts
        }

    def visualize_feature_distributions(self):
        """可视化特征分布"""
        print("\n分析特征分布...")

        df = pd.DataFrame(self.features_data)
        exclude_cols = ['file_path', 'fault_type', 'sensor_type', 'rpm', 'original_fs', 'resampled_fs']
        feature_cols = [col for col in df.columns if col not in exclude_cols]

        # 选择几个重要的特征进行可视化
        important_features = [
            'DE_rms', 'DE_kurtosis', 'DE_peak', 'DE_spectral_centroid',
            'DE_BPFO_amplitude', 'DE_BPFI_amplitude', 'DE_BSF_amplitude'
        ]

        available_features = [f for f in important_features if f in feature_cols]
        if len(available_features) < 4:
            # 如果重要特征不够，选择前几个特征
            available_features = feature_cols[:6]

        # 定义颜色映射 - 为不同故障类型设置不同颜色
        fault_types = df['fault_type'].unique()
        colors = ['#FF6B6B', "#4E16B0", "#D40E4D", "#DA12DA", "#D25F0D", "#E9BD0D"]
        color_map = {fault_type: colors[i % len(colors)] for i, fault_type in enumerate(fault_types)}

        # 创建子图
        n_features = min(6, len(available_features))
        fig, axes = plt.subplots(2, 3, figsize=(18, 12))
        axes = axes.flatten()

        for i, feature in enumerate(available_features[:n_features]):
            ax = axes[i]

            # 按故障类型分组绘制直方图，使用自定义颜色
            for fault_type in fault_types:
                data = df[df['fault_type'] == fault_type][feature]
                ax.hist(data, alpha=0.7, label=fault_type, bins=30,
                       color=color_map[fault_type], edgecolor='white', linewidth=0.5)

            ax.set_xlabel(feature, fontsize=10)
            ax.set_ylabel('频数', fontsize=10)
            ax.set_title(f'{feature} 分布', fontsize=12, fontweight='bold')
            ax.legend(fontsize=9)
            ax.grid(True, alpha=0.3)

        # 隐藏多余的子图
        for i in range(n_features, len(axes)):
            axes[i].set_visible(False)

        plt.tight_layout()
        self.save_figure(fig, 'feature_distributions.png')
        print(f"特征分布图已保存")

    def analyze_fault_type_differences(self):
        """分析故障类型间的差异"""
        print("\n分析故障类型差异...")

        df = pd.DataFrame(self.features_data)
        exclude_cols = ['file_path', 'fault_type', 'sensor_type', 'rpm', 'original_fs', 'resampled_fs']
        feature_cols = [col for col in df.columns if col not in exclude_cols]

        # 按故障类型计算统计信息
        fault_stats = {}
        for fault_type in df['fault_type'].unique():
            fault_data = df[df['fault_type'] == fault_type][feature_cols]
            fault_stats[fault_type] = {
                'mean': fault_data.mean(),
                'std': fault_data.std(),
                'count': len(fault_data)
            }

        # 选择几个关键特征进行可视化比较
        key_features = ['DE_rms', 'DE_kurtosis', 'DE_peak', 'DE_spectral_centroid']
        available_key_features = [f for f in key_features if f in feature_cols]

        if len(available_key_features) < 2:
            available_key_features = feature_cols[:4]

        # 定义颜色映射 - 与 visualize_feature_distributions 保持一致
        fault_types = list(fault_stats.keys())
        colors = ['#FF6B6B', "#4E16B0", "#D40E4D", "#DA12DA", "#D25F0D", "#E9BD0D"]
        color_map = {fault_type: colors[i % len(colors)] for i, fault_type in enumerate(fault_types)}

        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        axes = axes.flatten()

        for i, feature in enumerate(available_key_features[:4]):
            ax = axes[i]

            means = [fault_stats[ft]['mean'][feature] for ft in fault_types]
            stds = [fault_stats[ft]['std'][feature] for ft in fault_types]

            # 为每个柱子设置不同颜色
            bar_colors = [color_map[ft] for ft in fault_types]
            bars = ax.bar(fault_types, means, yerr=stds, capsize=5, alpha=0.8,
                         color=bar_colors, edgecolor='white', linewidth=1)

            ax.set_ylabel(feature, fontsize=10)
            ax.set_title(f'{feature} 按故障类型对比', fontsize=12, fontweight='bold')
            ax.grid(True, alpha=0.3)

            # 旋转x轴标签以避免重叠
            # ax.tick_params(axis='x', rotation=45)

            # 添加数值标签
            for bar, mean in zip(bars, means):
                ax.text(bar.get_x() + bar.get_width() / 2, bar.get_height() + bar.get_height() * 0.05,
                        f'{mean:.3f}', ha='center', va='bottom', fontsize=9)

        plt.tight_layout()
        self.save_figure(fig, 'fault_type_comparison.png')
        print(f"故障类型对比图已保存")

        # 打印统计结果
        print(f"\n故障类型统计对比:")
        for fault_type, stats in fault_stats.items():
            print(f"\n{fault_type}:")
            print(f"  样本数: {stats['count']}")
            for feature in available_key_features:
                print(f"  {feature}: {stats['mean'][feature]:.4f} ± {stats['std'][feature]:.4f}")

        return fault_stats

    def analyze_sampling_rate_effects(self):
        """分析采样率对特征的影响"""
        print("\n分析采样率影响...")

        df = pd.DataFrame(self.features_data)
        exclude_cols = ['file_path', 'fault_type', 'sensor_type', 'rpm', 'original_fs', 'resampled_fs']
        feature_cols = [col for col in df.columns if col not in exclude_cols]

        # 按原始采样率分组
        fs_groups = df.groupby('original_fs')

        analysis_features = ['DE_rms', 'DE_spectral_centroid', 'DE_BPFO_amplitude', 'DE_wavelet_entropy']
        available_features = [f for f in analysis_features if f in feature_cols]

        if len(available_features) < 2:
            available_features = feature_cols[:4]

        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        axes = axes.flatten()

        for i, feature in enumerate(available_features[:4]):
            ax = axes[i]

            data_by_fs = []
            labels = []

            for fs, group in fs_groups:
                data_by_fs.append(group[feature].values)
                labels.append(f'{fs} Hz')

            bp = ax.boxplot(data_by_fs, labels=labels, patch_artist=True)

            # 使用与 visualize_feature_distributions 相同的调色盘
            colors = ['#FF6B6B', "#4E16B0", "#D40E4D", "#DA12DA", "#D25F0D", "#E9BD0D"]
            for patch, color in zip(bp['boxes'], colors[:len(bp['boxes'])]):
                patch.set_facecolor(color)

            ax.set_ylabel(feature)
            ax.set_title(f'{feature} 按采样率分布')
            ax.grid(True, alpha=0.3)

        plt.tight_layout()
        self.save_figure(fig, 'sampling_rate_effects.png')
        print(f"采样率影响分析图已保存")

        # 统计分析
        print(f"\n采样率影响统计:")
        for feature in available_features:
            print(f"\n{feature}:")
            for fs, group in fs_groups:
                mean_val = group[feature].mean()
                std_val = group[feature].std()
                print(f"  {fs} Hz: {mean_val:.4f} ± {std_val:.4f}")

    def analyze_feature_correlations(self):
        """分析特征相关性"""
        print("\n分析特征相关性...")

        df = pd.DataFrame(self.features_data)
        exclude_cols = ['file_path', 'fault_type', 'sensor_type', 'rpm', 'original_fs', 'resampled_fs']
        feature_cols = [col for col in df.columns if col not in exclude_cols]

        # 计算相关性矩阵
        correlation_matrix = df[feature_cols].corr()

        # 寻找高相关性的特征对
        high_corr_pairs = []
        for i in range(len(correlation_matrix.columns)):
            for j in range(i + 1, len(correlation_matrix.columns)):
                corr_val = correlation_matrix.iloc[i, j]
                if abs(corr_val) > 0.8:  # 高相关性阈值
                    high_corr_pairs.append((
                        correlation_matrix.columns[i],
                        correlation_matrix.columns[j],
                        corr_val
                    ))

        print(f"发现 {len(high_corr_pairs)} 对高相关性特征 (|r| > 0.8):")
        for feat1, feat2, corr in high_corr_pairs[:10]:  # 只显示前10对
            print(f"  {feat1} - {feat2}: {corr:.3f}")

        # 绘制相关性热图（选择部分特征）
        n_features_to_show = min(20, len(feature_cols))
        selected_features = feature_cols[:n_features_to_show]

        # 使用与 visualize_feature_distributions 相同的调色盘创建渐变色彩映射
        colors_palette = ['#FF6B6B', "#4E16B0", "#D40E4D", "#DA12DA", "#D25F0D", "#E9BD0D"]
        # 为相关性热图创建从负相关到正相关的色彩映射
        custom_colors = [colors_palette[1], '#FFFFFF', colors_palette[3]]  # 蓝色-白色-红色渐变
        custom_cmap = LinearSegmentedColormap.from_list('custom_correlation', custom_colors, N=256)

        fig = plt.figure(figsize=(12, 10))
        sns.heatmap(correlation_matrix.loc[selected_features, selected_features],
                    annot=False, cmap=custom_cmap, center=0,
                    square=True, cbar_kws={"shrink": 0.8})
        plt.title('特征相关性矩阵', fontsize=14, fontweight='bold')
        plt.xticks(rotation=90)
        plt.yticks(rotation=0)
        plt.tight_layout()
        self.save_figure(fig, 'feature_correlation_heatmap.png')
        print(f"特征相关性热图已保存")

        return {
            'correlation_matrix': correlation_matrix,
            'high_corr_pairs': high_corr_pairs
        }

    def visualize_signal_examples(self):
        """可视化信号示例"""

        example_signals = {}
        example_spectra = {}

        for fault_type, file_list in self.selected_files.items():
            if file_list:
                # 选择第一个文件作为示例
                file_info = file_list[0]
                mat_data = self.load_mat_file(file_info['path'])

                if mat_data and mat_data['DE'] is not None:
                    # 取前8192个点进行可视化
                    signal = mat_data['DE'][:8192]
                    fs = file_info['fs']

                    # 重采样到统一采样率便于比较
                    if fs != self.target_fs:
                        signal = self.resample_signal(signal, fs, self.target_fs)

                    example_signals[fault_type] = signal

                    # 计算频谱
                    N = len(signal)
                    fft_vals = fft(signal)
                    freqs = fftfreq(N, 1 / self.target_fs)[:N // 2]
                    magnitude = np.abs(fft_vals[:N // 2])

                    example_spectra[fault_type] = (freqs, magnitude)

        # 定义颜色映射 - 与 visualize_feature_distributions 保持一致
        fault_types = list(example_signals.keys())
        colors = ['#FF6B6B', "#4E16B0", "#D40E4D", "#DA12DA", "#D25F0D", "#E9BD0D"]
        color_map = {fault_type: colors[i % len(colors)] for i, fault_type in enumerate(fault_types)}

        # 绘制时域信号
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        axes = axes.flatten()

        for i, (fault_type, signal) in enumerate(example_signals.items()):
            if i < 4:
                ax = axes[i]
                time_axis = np.arange(len(signal)) / self.target_fs
                ax.plot(time_axis, signal, color=color_map[fault_type], linewidth=0.8)
                ax.set_xlabel('时间 (s)')
                ax.set_ylabel('幅值')
                ax.set_title(f'{fault_type} 故障时域信号')
                ax.grid(True, alpha=0.3)

        for i in range(len(example_signals), len(axes)):
            axes[i].set_visible(False)

        plt.tight_layout()
        self.save_figure(fig, 'signal_examples_time_domain.png')

        # 绘制频域谱图
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        axes = axes.flatten()

        for i, (fault_type, (freqs, magnitude)) in enumerate(example_spectra.items()):
            if i < 4:
                ax = axes[i]
                # 只显示0-5000Hz的频率范围
                freq_mask = freqs <= 5000
                ax.semilogy(freqs[freq_mask], magnitude[freq_mask],
                           color=color_map[fault_type], linewidth=1.2)
                ax.set_xlabel('频率 (Hz)')
                ax.set_ylabel('幅值 (log)')
                ax.set_title(f'{fault_type} 故障频域谱图')
                ax.grid(True, alpha=0.3)

        for i in range(len(example_spectra), len(axes)):
            axes[i].set_visible(False)

        plt.tight_layout()
        self.save_figure(fig, 'signal_examples_frequency_domain.png')

        print(f"信号示例图已保存:")
        print(f"  时域信号和频域谱图已输出到文件")

    def generate_comprehensive_analysis_report(self):
        """生成综合分析报告"""

        # 基本统计
        basic_stats = self.analyze_basic_statistics()

        # 故障类型分析
        fault_stats = self.analyze_fault_type_differences()

        # 相关性分析
        correlation_results = self.analyze_feature_correlations()

        # 生成报告文本
        report_lines = [
            "# 轴承故障诊断数据分析报告",
            "=" * 50,
            "",
            "## 数据概况",
            f"- 总样本数: {len(self.features_data)}",
            f"- 特征维度: {len([col for col in pd.DataFrame(self.features_data).columns if col not in ['file_path', 'fault_type', 'sensor_type', 'rpm', 'original_fs', 'resampled_fs']])}",
            "",
            "## 故障类型分布",
        ]

        # 添加故障类型统计
        if basic_stats:
            for fault_type, count in basic_stats['fault_counts'].items():
                percentage = count / len(self.features_data) * 100
                report_lines.append(f"- {fault_type}: {count} ({percentage:.1f}%)")

        report_lines.extend([
            "",
            "## 采样率分布",
        ])

        # 添加采样率统计
        if basic_stats:
            for fs, count in basic_stats['fs_counts'].items():
                percentage = count / len(self.features_data) * 100
                report_lines.append(f"- {fs} Hz: {count} ({percentage:.1f}%)")

        # 打印报告内容
        report_text = "\n".join(report_lines)
        print(f"综合分析报告:")
        print(report_text)

        return report_text


def main():
    """主函数"""
    print("=" * 80)
    print("任务1: 源域数据筛选与特征提取（增强版 - 分类别异常值处理）")
    print("=" * 80)

    source_data_path = r"源域数据集"

    processor = BearingDataProcessor(source_data_path)

    # 设置处理参数
    processor.window_size = 4096
    processor.overlap_ratio = 0.5
    processor.target_fs = 32000  # 目标采样率32kHz

    # 步骤1: 筛选源域数据（混合12kHz和48kHz）
    print("步骤1: 筛选源域数据...")
    processor.select_source_data()

    # 步骤2: 特征提取（包含重采样）
    print("\n步骤2: 特征提取...")
    processor.process_all_selected_files()

    # 步骤2.5: 分类别异常值处理
    print("\n步骤2.5: 分类别异常值处理...")
    processor.remove_outliers(
        method='both',  # 使用IQR和Z-score两种方法
        iqr_factor=1.5,  # IQR倍数因子
        z_threshold=3,  # Z-score阈值
        max_removal_ratio=0.1  # 每个类别最大移除10%
    )

    # 步骤3: 数据分析和可视化
    print("\n步骤3: 数据分析和可视化...")

    # 3.1 基本统计分析
    processor.analyze_basic_statistics()

    # 3.2 特征分布可视化
    # processor.visualize_feature_distributions()

    # 3.3 故障类型差异分析
    # processor.analyze_fault_type_differences()

    # 3.4 采样率影响分析
    # processor.analyze_sampling_rate_effects()

    # 3.5 特征相关性分析
    processor.analyze_feature_correlations()

    # 3.6 信号示例可视化
    # processor.visualize_signal_examples()

    # 步骤4: 生成综合分析报告
    print("\n步骤4: 生成综合分析报告...")
    # processor.generate_comprehensive_analysis_report()

    print(f"\n任务1增强版完成！所有分析结果已在控制台展示")


if __name__ == "__main__":
    main()
