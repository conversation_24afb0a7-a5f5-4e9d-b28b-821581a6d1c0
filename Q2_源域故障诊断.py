import os
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from matplotlib.colors import LinearSegmentedColormap
import seaborn as sns
from sklearn.model_selection import train_test_split, StratifiedKFold, GridSearchCV
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier
from sklearn.svm import SVC
from sklearn.neural_network import MLPClassifier
from sklearn.metrics import classification_report, confusion_matrix, accuracy_score
from sklearn.metrics import precision_recall_fscore_support, roc_auc_score, roc_curve
from sklearn.feature_selection import SelectKBest, f_classif, RFE
from sklearn.decomposition import PCA
import warnings

warnings.filterwarnings('ignore')

plt.rcParams['font.sans-serif'] = ['PingFang SC', 'SimHei', 'STHeiti', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False


class SourceDomainDiagnosisModel:
    def __init__(self, data_path='processed_data_mixed_fs'):
        self.data_path = data_path
        self.df = None
        self.X = None
        self.y = None
        self.X_train = None
        self.X_test = None
        self.y_train = None
        self.y_test = None
        self.scaler = StandardScaler()
        self.label_encoder = LabelEncoder()
        self.models = {}
        self.results = {}
        self.output_dir = os.path.join(os.getcwd(), "图片Q2")

    def save_figure(self, fig, filename):
        """Save a figure to the dedicated output directory."""
        os.makedirs(self.output_dir, exist_ok=True)
        filepath = os.path.join(self.output_dir, filename)
        fig.savefig(filepath, dpi=300, bbox_inches='tight')
        plt.close(fig)
        print(f"图像已保存: {filepath}")

    def load_data(self):
        """加载特征数据"""
        print("=" * 60)
        print("加载源域特征数据（混合采样率）")
        print("=" * 60)

        # 加载完整特征数据
        feature_file = f"{self.data_path}/extracted_features.csv"
        self.df = pd.read_csv(feature_file)

        print(f"数据集大小: {self.df.shape}")
        print(f"\n故障类型分布:")
        fault_counts = self.df['fault_type'].value_counts()
        for fault, count in fault_counts.items():
            print(f"  {fault}: {count} 样本")

        # 显示原始采样率分布
        if 'original_fs' in self.df.columns:
            print(f"\n原始采样率分布:")
            fs_counts = self.df['original_fs'].value_counts()
            for fs, count in fs_counts.items():
                print(f"  {fs} Hz: {count} 样本")

        return self.df

    def preprocess_data(self):
        """数据预处理"""
        print(f"\n数据预处理...")

        # 选择特征列（排除非特征列）
        exclude_cols = ['file_path', 'fault_type', 'sensor_type', 'original_fs', 'resampled_fs']
        feature_cols = [col for col in self.df.columns if col not in exclude_cols]

        print(f"排除列: {exclude_cols}")
        print(f"特征列数量: {len(feature_cols)}")

        self.X = self.df[feature_cols].values
        self.y = self.label_encoder.fit_transform(self.df['fault_type'].values)

        print(f"特征维度: {self.X.shape[1]}")
        print(
            f"类别映射: {dict(zip(self.label_encoder.classes_, self.label_encoder.transform(self.label_encoder.classes_)))}")

        # 检查缺失值和异常值
        nan_count = np.isnan(self.X).sum()
        inf_count = np.isinf(self.X).sum()

        if nan_count > 0:
            print(f"发现 {nan_count} 个缺失值，用列均值填充")
            # 按列计算均值并填充
            col_means = np.nanmean(self.X, axis=0)
            for i in range(self.X.shape[1]):
                mask = np.isnan(self.X[:, i])
                self.X[mask, i] = col_means[i]

        if inf_count > 0:
            print(f"发现 {inf_count} 个无穷值，用有限值替换")
            # 处理正无穷和负无穷
            finite_mask = np.isfinite(self.X)
            for i in range(self.X.shape[1]):
                col_data = self.X[:, i]
                finite_col = col_data[finite_mask[:, i]]
                if len(finite_col) > 0:
                    max_val = np.max(finite_col)
                    min_val = np.min(finite_col)
                    self.X[np.isposinf(col_data), i] = max_val
                    self.X[np.isneginf(col_data), i] = min_val

        # 再次检查
        nan_count_after = np.isnan(self.X).sum()
        inf_count_after = np.isinf(self.X).sum()
        if nan_count_after > 0 or inf_count_after > 0:
            print(f"警告：处理后仍有 {nan_count_after} 个NaN值和 {inf_count_after} 个无穷值")
            # 使用0填充剩余的异常值
            self.X = np.nan_to_num(self.X, nan=0.0, posinf=1e10, neginf=-1e10)

        return self.X, self.y

    def split_data(self, test_size=0.2, random_state=42):
        """分层划分训练集和测试集（减少数据泄露）"""
        print(f"\n数据集划分（减少文件重叠）...")

        # 添加文件分组信息
        self.df['base_file'] = self.df['file_path'].apply(lambda x: x.split('_sample_')[0])
        self.df['sample_idx'] = self.df['file_path'].apply(
            lambda x: int(x.split('_sample_')[1]) if '_sample_' in x else 0
        )

        # 统计信息
        n_files = self.df['base_file'].nunique()
        samples_per_file = len(self.df) / n_files
        print(f"原始文件数: {n_files}")
        print(f"每个文件平均样本数: {samples_per_file:.1f}")

        # 混合策略：部分文件完全隔离，部分文件内部划分
        train_indices = []
        test_indices = []

        # 对每种故障类型分别处理，确保测试集中有各种故障类型
        for fault_type in self.df['fault_type'].unique():
            fault_data = self.df[self.df['fault_type'] == fault_type]
            fault_files = fault_data['base_file'].unique()

            if len(fault_files) < 3:
                print(f"\n{fault_type} 文件数较少({len(fault_files)})，使用时间序列划分")
                for file in fault_files:
                    file_data = fault_data[fault_data['base_file'] == file].sort_values('sample_idx')
                    file_indices = file_data.index.tolist()

                    n_train = int(len(file_indices) * (1 - test_size))
                    train_indices.extend(file_indices[:n_train])
                    test_indices.extend(file_indices[n_train:])
            else:
                np.random.seed(random_state)
                np.random.shuffle(fault_files)
                n_test_files = max(1, int(len(fault_files) * test_size))
                test_files = fault_files[:n_test_files]
                train_files = fault_files[n_test_files:]

                for file in test_files:
                    file_indices = fault_data[fault_data['base_file'] == file].index.tolist()
                    test_indices.extend(file_indices)

                for file in train_files:
                    file_data = fault_data[fault_data['base_file'] == file].sort_values('sample_idx')
                    file_indices = file_data.index.tolist()
                    train_indices.extend(file_indices)

        train_idx = np.array(train_indices)
        test_idx = np.array(test_indices)

        self.X_train = self.X[train_idx]
        self.X_test = self.X[test_idx]
        self.y_train = self.y[train_idx]
        self.y_test = self.y[test_idx]

        train_files = set(self.df.iloc[train_idx]['base_file'])
        test_files = set(self.df.iloc[test_idx]['base_file'])
        overlap_files = train_files.intersection(test_files)

        print(f"\n训练集大小: {self.X_train.shape[0]} 样本")
        print(f"测试集大小: {self.X_test.shape[0]} 样本")
        print(f"测试集比例: {len(test_idx) / (len(train_idx) + len(test_idx)) * 100:.1f}%")
        print(f"文件重叠数: {len(overlap_files)} (应为0)")

        train_counts = pd.Series(self.y_train).value_counts().sort_index()
        test_counts = pd.Series(self.y_test).value_counts().sort_index()

        print(f"\n训练集类别分布:")
        for i, count in train_counts.items():
            class_name = self.label_encoder.inverse_transform([i])[0]
            percentage = count / len(self.y_train) * 100
            print(f"  {class_name}: {count} 样本 ({percentage:.1f}%)")

        print(f"\n测试集类别分布:")
        for i, count in test_counts.items():
            class_name = self.label_encoder.inverse_transform([i])[0]
            percentage = count / len(self.y_test) * 100
            print(f"  {class_name}: {count} 样本 ({percentage:.1f}%)")


        if len(train_counts) < len(np.unique(self.y)) or len(test_counts) < len(np.unique(self.y)):
            print("\n警告：某些类别在训练集或测试集中缺失！")

        return self.X_train, self.X_test, self.y_train, self.y_test

    def normalize_features(self):
        """特征标准化"""
        print(f"\n特征标准化...")

        self.X_train = self.scaler.fit_transform(self.X_train)
        self.X_test = self.scaler.transform(self.X_test)

        print(f"标准化完成")
        print(f"训练集特征范围: [{np.min(self.X_train):.3f}, {np.max(self.X_train):.3f}]")
        print(f"训练集特征均值: {np.mean(self.X_train):.3f}, 标准差: {np.std(self.X_train):.3f}")

        return self.X_train, self.X_test

    def feature_selection(self, method='variance', k=50):
        """特征选择"""
        print(f"\n特征选择 - 方法: {method}")

        # 确保k不超过特征数
        k = min(k, self.X_train.shape[1])
        print(f"选择 {k} 个特征（总特征数: {self.X_train.shape[1]}）")

        if method == 'variance':
            # 方差过滤
            variances = np.var(self.X_train, axis=0)
            selected_idx = np.argsort(variances)[-k:]
            self.X_train = self.X_train[:, selected_idx]
            self.X_test = self.X_test[:, selected_idx]

        elif method == 'univariate':
            # 单变量统计检验
            selector = SelectKBest(score_func=f_classif, k=k)
            self.X_train = selector.fit_transform(self.X_train, self.y_train)
            self.X_test = selector.transform(self.X_test)

        elif method == 'rfe':

            estimator = RandomForestClassifier(n_estimators=50, random_state=42, n_jobs=-1)
            selector = RFE(estimator, n_features_to_select=k)
            self.X_train = selector.fit_transform(self.X_train, self.y_train)
            self.X_test = selector.transform(self.X_test)

        else:
            print(f"不进行特征选择")
            return self.X_train, self.X_test

        print(f"特征选择完成，新特征维度: {self.X_train.shape[1]}")
        return self.X_train, self.X_test

    def build_models(self):
        """构建多种诊断模型"""
        print(f"\n构建诊断模型...")

        # 获取类别权重
        n_classes = len(np.unique(self.y))
        input_shape = self.X_train.shape[1]

        self.models = {
            'RandomForest': RandomForestClassifier(
                n_estimators=300,
                max_depth=12,
                min_samples_split=5,
                min_samples_leaf=2,
                random_state=42,
                class_weight='balanced',
                n_jobs=-1
            ),

            'SVM': SVC(
                kernel='rbf',
                C=100000.0,
                gamma='scale',
                random_state=42,
                class_weight='balanced',
                probability=True
            ),

            'GradientBoosting': GradientBoostingClassifier(
                n_estimators=100,
                max_depth=5,
                learning_rate=0.1,
                random_state=42
            ),

            'MLP': MLPClassifier(
                hidden_layer_sizes=(100, 32, 32),
                activation='relu',
                solver='adam',
                learning_rate_init=0.1,
                max_iter=1000,
                random_state=42,
                early_stopping=True,
                validation_fraction=0.1
            )
        }

        print(f"构建了 {len(self.models)} 个模型:")
        for name in self.models.keys():
            print(f"  - {name}")

        return self.models

    def train_models(self):
        """训练所有模型"""
        print(f"\n开始训练模型...")
        print("=" * 60)

        trained_models = {}

        for name, model in self.models.items():
            print(f"训练 {name} 模型...")

            try:
                model.fit(self.X_train, self.y_train)
                trained_models[name] = model
                print(f"  {name} 训练完成")

            except Exception as e:
                print(f"  {name} 训练失败: {e}")

        self.models = trained_models
        print(f"\n成功训练了 {len(trained_models)} 个模型")

        return trained_models

    def evaluate_models(self):
        """评价所有模型"""
        print(f"\n模型评价")
        print("=" * 60)

        self.results = {}

        for name, model in self.models.items():
            print(f"\n评价 {name} 模型:")
            print("-" * 40)

            # 预测
            y_pred = model.predict(self.X_test)
            y_pred_proba = model.predict_proba(self.X_test) if hasattr(model, 'predict_proba') else None

            # 计算指标
            accuracy = accuracy_score(self.y_test, y_pred)
            precision, recall, f1, _ = precision_recall_fscore_support(
                self.y_test, y_pred, average='weighted', zero_division=0
            )

            # 多类别AUC计算
            if y_pred_proba is not None:
                try:
                    # 处理类别数量
                    n_classes = len(np.unique(self.y))
                    if n_classes > 2:
                        auc = roc_auc_score(self.y_test, y_pred_proba,
                                            multi_class='ovr', average='weighted')
                    else:
                        auc = roc_auc_score(self.y_test, y_pred_proba[:, 1])
                except Exception as e:
                    print(f"  AUC计算失败: {e}")
                    auc = None
            else:
                auc = None

            # 存储结果
            self.results[name] = {
                'accuracy': accuracy,
                'precision': precision,
                'recall': recall,
                'f1_score': f1,
                'auc': auc,
                'y_pred': y_pred,
                'y_pred_proba': y_pred_proba
            }
            # 打印结果
            print(f"准确率 (Accuracy): {accuracy:.4f}")
            print(f"精确率 (Precision): {precision:.4f}")
            print(f"召回率 (Recall): {recall:.4f}")
            print(f"F1分数 (F1-Score): {f1:.4f}")
            if auc is not None:
                print(f"AUC分数: {auc:.4f}")

            # 详细分类报告
            print(f"\n详细分类报告:")
            class_names = self.label_encoder.inverse_transform(np.unique(self.y_test))
            print(classification_report(self.y_test, y_pred,
                                        target_names=class_names,
                                        zero_division=0))

        return self.results

    def plot_confusion_matrices(self):
        """绘制所有模型的混淆矩阵"""
        print(f"\n绘制混淆矩阵...")

        n_models = len(self.models)
        fig, axes = plt.subplots(2, 2, figsize=(18, 12))
        axes = axes.ravel()

        class_names = self.label_encoder.inverse_transform(np.unique(self.y))
        colors_palette = ['#FF6B6B', "#4E16B0", "#D40E4D", "#DA12DA", "#D25F0D", "#E9BD0D"]
        # 为混淆矩阵创建从白色到主色调的渐变
        custom_colors = ['#FFFFFF', colors_palette[1]]  # 白色到蓝色渐变
        custom_cmap = LinearSegmentedColormap.from_list('custom_confusion', custom_colors, N=256)

        for i, (name, model) in enumerate(self.models.items()):
            if i >= len(axes):
                break

            y_pred = self.results[name]['y_pred']
            cm = confusion_matrix(self.y_test, y_pred)

            # 计算准确率
            accuracy = self.results[name]['accuracy']

            # 绘制混淆矩阵
            sns.heatmap(cm, annot=True, fmt='d', cmap=custom_cmap,
                        xticklabels=class_names, yticklabels=class_names,
                        ax=axes[i])
            axes[i].set_title(f'{name}\nAccuracy: {accuracy:.3f}', fontsize=12, fontweight='bold')
            axes[i].set_xlabel('Predicted', fontsize=10)
            axes[i].set_ylabel('Actual', fontsize=10)

        # 隐藏多余的子图
        for i in range(len(self.models), len(axes)):
            axes[i].set_visible(False)

        plt.tight_layout()
        self.save_figure(fig, 'confusion_matrices.png')

    def plot_model_comparison(self):
        """绘制模型性能对比图"""
        print(f"\n绘制模型性能对比...")

        # 提取性能指标
        models = list(self.results.keys())
        metrics = ['accuracy', 'precision', 'recall', 'f1_score']

        # 创建数据
        data = []
        for model in models:
            for metric in metrics:
                if metric in self.results[model] and self.results[model][metric] is not None:
                    data.append({
                        'Model': model,
                        'Metric': metric.replace('_', ' ').title(),
                        'Score': self.results[model][metric]
                    })

        df_results = pd.DataFrame(data)

        # 绘制条形图
        fig = plt.figure(figsize=(12, 8))

        # 使用与 Q1 文件相同的调色盘
        colors_palette = ['#FF6B6B', "#4E16B0", "#D40E4D", "#DA12DA", "#D25F0D", "#E9BD0D"]

        sns.barplot(data=df_results, x='Model', y='Score', hue='Metric', palette=colors_palette[:4])
        plt.title('模型性能对比', fontsize=14, fontweight='bold')
        plt.ylabel('分数', fontsize=12)
        plt.xlabel('模型', fontsize=12)
        plt.legend(title='评价指标', fontsize=10)
        plt.xticks(rotation=45)
        plt.ylim(0, 1.1)  # 设置y轴范围
        plt.grid(True, alpha=0.3)
        plt.tight_layout()
        self.save_figure(fig, 'model_comparison.png')
        print(f"模型性能对比图已保存")

        # 打印性能排名
        print(f"\n模型性能排名 (按F1分数):")
        f1_scores = [(name, result['f1_score']) for name, result in self.results.items()]
        f1_scores.sort(key=lambda x: x[1], reverse=True)

        for i, (name, score) in enumerate(f1_scores, 1):
            print(f"{i}. {name}: {score:.4f}")

    def feature_importance_analysis(self):
        """特征重要性分析"""
        print(f"\n特征重要性分析...")

        # 只分析支持特征重要性的模型（不包括1D-CNN）
        importance_models = ['RandomForest', 'GradientBoosting']

        fig, axes = plt.subplots(1, 2, figsize=(14, 6))

        # 使用与 Q1 文件相同的调色盘
        colors_palette = ['#FF6B6B', "#4E16B0", "#D40E4D", "#DA12DA", "#D25F0D", "#E9BD0D"]

        for i, model_name in enumerate(importance_models):
            if model_name in self.models:
                model = self.models[model_name]

                if hasattr(model, 'feature_importances_'):
                    importances = model.feature_importances_

                    # 获取前20个重要特征
                    n_features = min(20, len(importances))
                    top_indices = np.argsort(importances)[-n_features:]
                    top_importances = importances[top_indices]

                    # 绘制特征重要性
                    axes[i].barh(range(len(top_importances)), top_importances,
                                color=colors_palette[i % len(colors_palette)])
                    axes[i].set_title(f'{model_name} 特征重要性 (Top {n_features})',
                                     fontsize=12, fontweight='bold')
                    axes[i].set_xlabel('重要性', fontsize=10)
                    axes[i].set_ylabel('特征索引', fontsize=10)
                    axes[i].grid(True, alpha=0.3)

                    # 添加数值标签
                    for j, v in enumerate(top_importances):
                        axes[i].text(v, j, f'{v:.3f}', va='center', fontsize=8)

        plt.tight_layout()
        self.save_figure(fig, 'feature_importance.png')
        print(f"特征重要性图已保存")

    def hyperparameter_optimization(self, model_name='RandomForest'):
        """超参数优化"""
        print(f"\n{model_name} 超参数优化...")

        if model_name == 'RandomForest':
            param_grid = {
                'n_estimators': [50, 100, 200],
                'max_depth': [5, 10, 15],
                'min_samples_split': [2, 5, 10],
                'min_samples_leaf': [1, 2, 4]
            }
            base_model = RandomForestClassifier(random_state=42, class_weight='balanced')

        elif model_name == 'SVM':
            param_grid = {
                'C': [0.1, 1, 10],
                'gamma': ['scale', 'auto', 0.001, 0.01],
                'kernel': ['rbf']
            }
            base_model = SVC(random_state=42, class_weight='balanced', probability=True)

        else:
            print(f"不支持 {model_name} 的超参数优化")
            return None

        # 网格搜索
        cv = StratifiedKFold(n_splits=3, shuffle=True, random_state=42)
        grid_search = GridSearchCV(
            base_model, param_grid,
            cv=cv, scoring='f1_weighted',
            n_jobs=-1, verbose=1
        )

        grid_search.fit(self.X_train, self.y_train)

        print(f"最佳参数: {grid_search.best_params_}")
        print(f"最佳交叉验证分数: {grid_search.best_score_:.4f}")

        # 用最佳模型评价测试集
        best_model = grid_search.best_estimator_
        y_pred = best_model.predict(self.X_test)
        accuracy = accuracy_score(self.y_test, y_pred)

        print(f"测试集准确率: {accuracy:.4f}")

        return best_model, grid_search.best_params_

    def save_results(self):
        """保存评价结果"""
        print(f"\n保存评价结果...")

        # 保存模型性能结果
        results_df = pd.DataFrame({
            'Model': list(self.results.keys()),
            'Accuracy': [r['accuracy'] for r in self.results.values()],
            'Precision': [r['precision'] for r in self.results.values()],
            'Recall': [r['recall'] for r in self.results.values()],
            'F1_Score': [r['f1_score'] for r in self.results.values()],
            'AUC': [r['auc'] if r['auc'] is not None else np.nan for r in self.results.values()]
        })

        results_df.to_csv(f'{self.data_path}/model_evaluation_results.csv', index=False)

        # 保存预测结果
        for name, result in self.results.items():
            pred_df = pd.DataFrame({
                'True_Label': self.y_test,
                'Predicted_Label': result['y_pred'],
                'True_Class': self.label_encoder.inverse_transform(self.y_test),
                'Predicted_Class': self.label_encoder.inverse_transform(result['y_pred'])
            })

            if result['y_pred_proba'] is not None:
                prob_cols = [f'Prob_{cls}' for cls in self.label_encoder.classes_]
                prob_df = pd.DataFrame(result['y_pred_proba'], columns=prob_cols)
                pred_df = pd.concat([pred_df, prob_df], axis=1)

            pred_df.to_csv(f'{self.data_path}/predictions_{name}.csv', index=False)

        print(f"结果已保存到 {self.data_path} 目录")

        return results_df


def main():
    """主函数"""
    print("=" * 80)
    print("任务2: 源域故障诊断模型设计与评价（深度学习增强版）")
    print("=" * 80)

    # 创建诊断模型，使用问题1的输出目录
    diagnosis = SourceDomainDiagnosisModel(data_path='processed_data_mixed_fs')

    # 步骤1: 加载数据
    diagnosis.load_data()

    # 步骤2: 数据预处理
    diagnosis.preprocess_data()

    # 步骤3: 划分训练测试集
    diagnosis.split_data(test_size=0.2, random_state=42)

    # 步骤4: 特征标准化
    diagnosis.normalize_features()

    # 步骤5: 特征选择（可选）
    # 由于特征维度可能较高，建议进行特征选择
    diagnosis.feature_selection(method='univariate', k=50)

    # 步骤6: 构建模型
    diagnosis.build_models()

    # 步骤7: 训练模型
    diagnosis.train_models()

    # 步骤8: 评价模型
    diagnosis.evaluate_models()

    # 步骤9: 可视化结果
    diagnosis.plot_confusion_matrices()
    diagnosis.plot_model_comparison()
    diagnosis.feature_importance_analysis()

    # 步骤10: 超参数优化（可选）
    print(f"\n进行超参数优化...")
    best_rf, best_params = diagnosis.hyperparameter_optimization('RandomForest')

    # 步骤11: 保存结果
    diagnosis.save_results()

    print(f"\n任务2完成！")
    print(f"\n最佳模型性能:")
    best_model = max(diagnosis.results.items(), key=lambda x: x[1]['f1_score'])
    print(f"  模型: {best_model[0]}")
    print(f"  F1分数: {best_model[1]['f1_score']:.4f}")
    print(f"  准确率: {best_model[1]['accuracy']:.4f}")

if __name__ == "__main__":
    main()
