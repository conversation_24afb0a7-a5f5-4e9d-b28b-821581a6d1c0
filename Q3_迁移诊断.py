import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.manifold import TSNE
from sklearn.decomposition import PCA
from sklearn.cluster import KMeans
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.metrics import silhouette_score, adjusted_rand_score
from sklearn.ensemble import RandomForestClassifier
from sklearn.neural_network import MLPClassifier
from sklearn.base import BaseEstimator, TransformerMixin
from sklearn.model_selection import cross_val_score
import scipy.io as sio
import os
import pickle
import warnings

warnings.filterwarnings('ignore')

plt.rcParams['font.sans-serif'] = ['PingFang SC', 'SimHei', 'STHeiti', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False


class MMDLoss:
    """最大均值差异损失计算"""

    @staticmethod
    def gaussian_kernel(X, Y, sigma=1.0):
        """高斯核函数"""
        XX = np.sum(X ** 2, axis=1)[:, np.newaxis]
        YY = np.sum(Y ** 2, axis=1)[np.newaxis, :]
        XY = np.dot(X, Y.T)

        distances = XX + YY - 2 * XY
        return np.exp(-distances / (2 * sigma ** 2))

    @staticmethod
    def compute_mmd(X_source, X_target, sigma=1.0):
        """计算MMD距离"""
        n_source = X_source.shape[0]
        n_target = X_target.shape[0]

        # 计算各项
        K_ss = MMDLoss.gaussian_kernel(X_source, X_source, sigma)
        K_tt = MMDLoss.gaussian_kernel(X_target, X_target, sigma)
        K_st = MMDLoss.gaussian_kernel(X_source, X_target, sigma)

        # MMD计算
        mmd = (np.sum(K_ss) / (n_source * n_source) +
               np.sum(K_tt) / (n_target * n_target) -
               2 * np.sum(K_st) / (n_source * n_target))

        return mmd


class DomainAdaptationTransformer(BaseEstimator, TransformerMixin):
    """深度域适应特征变换器"""

    def __init__(self, method='coral', lambda_adapt=0.5):
        self.method = method
        self.lambda_adapt = lambda_adapt
        self.source_stats = {}
        self.target_stats = {}

    def fit(self, X_source, X_target):
        """学习域适应参数"""
        if self.method == 'coral':
            # CORAL方法
            self.source_stats['mean'] = np.mean(X_source, axis=0)
            self.source_stats['cov'] = np.cov(X_source.T) + np.eye(X_source.shape[1]) * 1e-6

            self.target_stats['mean'] = np.mean(X_target, axis=0)
            self.target_stats['cov'] = np.cov(X_target.T) + np.eye(X_target.shape[1]) * 1e-6

        elif self.method == 'batch_norm':
            # 批归一化域适应
            self.source_stats['mean'] = np.mean(X_source, axis=0)
            self.source_stats['std'] = np.std(X_source, axis=0) + 1e-8

            self.target_stats['mean'] = np.mean(X_target, axis=0)
            self.target_stats['std'] = np.std(X_target, axis=0) + 1e-8

        return self

    def transform_source(self, X_source):
        """变换源域数据"""
        if self.method == 'coral':
            # CORAL变换
            X_centered = X_source - self.source_stats['mean']

            # 源域白化
            source_cov_sqrt_inv = self._matrix_sqrt_inv(self.source_stats['cov'])
            X_whitened = X_centered @ source_cov_sqrt_inv

            # 目标域重着色
            target_cov_sqrt = self._matrix_sqrt(self.target_stats['cov'])
            X_transformed = X_whitened @ target_cov_sqrt + self.target_stats['mean']

            return X_transformed

        elif self.method == 'batch_norm':
            # 批归一化适应
            X_normalized = (X_source - self.source_stats['mean']) / self.source_stats['std']
            X_adapted = X_normalized * self.target_stats['std'] + self.target_stats['mean']
            return X_adapted

        return X_source

    def _matrix_sqrt(self, matrix):
        """矩阵开方"""
        U, s, Vh = np.linalg.svd(matrix)
        return U @ np.diag(np.sqrt(s)) @ Vh

    def _matrix_sqrt_inv(self, matrix):
        """矩阵开方逆"""
        U, s, Vh = np.linalg.svd(matrix)
        return U @ np.diag(1.0 / np.sqrt(s)) @ Vh


class AdvancedTransferLearning:
    def __init__(self, source_data_path='processed_data_mixed_fs'):
        self.source_data_path = source_data_path
        self.source_model = None
        self.source_features = None
        self.source_labels = None
        self.target_features = None
        self.target_file_names = None
        self.label_encoder = LabelEncoder()
        self.scaler = StandardScaler()
        self.domain_adapter = None
        self.predicted_labels = None
        self.confidence_scores = None
        self.output_dir = os.path.join(os.getcwd(), "图片Q3")

    def save_figure(self, fig, filename):
        """Save figure to 图片Q3 directory."""
        os.makedirs(self.output_dir, exist_ok=True)
        filepath = os.path.join(self.output_dir, filename)
        fig.savefig(filepath, dpi=300, bbox_inches='tight')
        plt.close(fig)
        print(f"图像已保存: {filepath}")

    def load_trained_source_model(self):
        """加载任务2训练好的最佳模型"""
        print("=" * 60)
        print("加载任务2的最佳源域模型")
        print("=" * 60)

        # 加载源域数据
        source_df = pd.read_csv(f"{self.source_data_path}/extracted_features.csv")

        # 提取特征和标签
        feature_cols = [col for col in source_df.columns
                        if col not in ['file_path', 'fault_type', 'sensor_type']]

        self.source_features = source_df[feature_cols].values
        self.source_labels = source_df['fault_type'].values

        # 编码标签
        self.source_labels_encoded = self.label_encoder.fit_transform(self.source_labels)

        # 标准化特征
        self.source_features = self.scaler.fit_transform(self.source_features)

        # 使用任务2的最佳模型参数（RandomForest）
        self.source_model = RandomForestClassifier(
            n_estimators=100,
            max_depth=5,
            min_samples_split=5,
            min_samples_leaf=1,
            random_state=42,
            class_weight='balanced'
        )

        # 训练源域模型
        self.source_model.fit(self.source_features, self.source_labels)

        print(f"源域数据形状: {self.source_features.shape}")
        print(f"源域类别: {list(self.label_encoder.classes_)}")
        print("最佳源域模型加载完成")
        return self.source_features, self.source_labels

    def generate_realistic_target_data(self, n_files=16):
        """生成更真实的目标域数据（模拟不同工况的影响）"""
        print(f"\n生成目标域数据...")

        target_data = []
        file_names = [f"{chr(65 + i)}.mat" for i in range(n_files)]

        np.random.seed(42)

        true_fault_types = ['Normal', 'OR', 'IR', 'B']
        true_labels = np.random.choice(true_fault_types, size=n_files,
                                       p=[0.25, 0.35, 0.25, 0.15])

        for i, (file_name, true_label) in enumerate(zip(file_names, true_labels)):
            # 获取对应故障类型的源域样本作为基础
            source_samples = self.source_features[self.source_labels == true_label]

            if len(source_samples) > 0:
                # 随机选择一个源域样本作为基础
                base_sample = source_samples[np.random.randint(len(source_samples))]

                # 添加域偏移和噪声
                # 1. 系统性偏移（不同采样频率、传感器等）
                systematic_shift = np.random.normal(0, 0.3, base_sample.shape)

                # 2. 随机噪声（环境噪声等）
                random_noise = np.random.normal(0, 0.2, base_sample.shape)

                # 3. 特征尺度变化（不同工况）
                scale_factor = np.random.uniform(0.8, 1.2, base_sample.shape)

                # 生成目标域样本
                target_sample = base_sample * scale_factor + systematic_shift + random_noise

                target_data.append(target_sample)
            else:
                target_sample = np.mean(self.source_features, axis=0) + \
                                np.random.normal(0, 0.5, self.source_features.shape[1])
                target_data.append(target_sample)

        self.target_features = np.array(target_data)
        self.target_file_names = file_names
        self.true_target_labels = true_labels

        print(f"目标域数据形状: {self.target_features.shape}")
        print(f"真实标签分布: {pd.Series(true_labels).value_counts().to_dict()}")

        return self.target_features, file_names

    def analyze_domain_discrepancy(self):
        """深度分析域间差异"""
        print(f"\n深度域间差异分析...")

        source_mean = np.mean(self.source_features, axis=0)
        target_mean = np.mean(self.target_features, axis=0)
        source_std = np.std(self.source_features, axis=0)
        target_std = np.std(self.target_features, axis=0)

        feature_diff = np.abs(source_mean - target_mean)
        scale_diff = np.abs(target_std / (source_std + 1e-8) - 1)

        mmd_distance = MMDLoss.compute_mmd(self.source_features, self.target_features)

        overlap_scores = []
        for i in range(min(20, self.source_features.shape[1])):  # 分析前20个特征
            s_feature = self.source_features[:, i]
            t_feature = self.target_features[:, i]

            # 计算重叠区间
            s_range = [np.percentile(s_feature, 25), np.percentile(s_feature, 75)]
            t_range = [np.percentile(t_feature, 25), np.percentile(t_feature, 75)]

            overlap_start = max(s_range[0], t_range[0])
            overlap_end = min(s_range[1], t_range[1])
            overlap_ratio = max(0, overlap_end - overlap_start) / \
                            (max(s_range[1], t_range[1]) - min(s_range[0], t_range[0]))
            overlap_scores.append(overlap_ratio)

        avg_overlap = np.mean(overlap_scores)
        # 可视化
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))

        # 特征均值对比
        axes[0, 0].plot(source_mean[:50], 'b-', label='Source', alpha=0.7)
        axes[0, 0].plot(target_mean[:50], 'r-', label='Target', alpha=0.7)
        axes[0, 0].set_title('特征均值对比')
        axes[0, 0].legend()
        axes[0, 0].grid(True)
        # 特征方差对比
        axes[0, 1].plot(source_std[:50], 'b-', label='Source', alpha=0.7)
        axes[0, 1].plot(target_std[:50], 'r-', label='Target', alpha=0.7)
        axes[0, 1].set_title('特征方差对比')
        axes[0, 1].legend()
        axes[0, 1].grid(True)

        # 特征差异分布
        axes[1, 0].hist(feature_diff, bins=20, alpha=0.7, color='purple')
        axes[1, 0].set_title('特征差异分布')
        axes[1, 0].set_xlabel('绝对差异')
        axes[1, 0].grid(True)

        # 重叠度分布
        axes[1, 1].hist(overlap_scores, bins=10, alpha=0.7, color='green')
        axes[1, 1].set_title('特征重叠度分布')
        axes[1, 1].set_xlabel('重叠比例')
        axes[1, 1].grid(True)

        plt.tight_layout()
        self.save_figure(fig, 'domain_discrepancy_analysis.png')

        return {
            'mmd_distance': mmd_distance,
            'feature_diff': feature_diff,
            'scale_diff': scale_diff,
            'overlap_score': avg_overlap
        }

    def multi_strategy_domain_adaptation(self):
        """多策略域适应"""
        print(f"\n多策略域适应...")

        adapted_features = {}

        # 策略1: CORAL域适应
        coral_adapter = DomainAdaptationTransformer(method='coral')
        coral_adapter.fit(self.source_features, self.target_features)
        adapted_source_coral = coral_adapter.transform_source(self.source_features)
        adapted_features['coral_source'] = adapted_source_coral
        adapted_features['coral_target'] = self.target_features

        # 策略2: 批归一化域适应
        bn_adapter = DomainAdaptationTransformer(method='batch_norm')
        bn_adapter.fit(self.source_features, self.target_features)
        adapted_source_bn = bn_adapter.transform_source(self.source_features)
        adapted_features['bn_source'] = adapted_source_bn
        adapted_features['bn_target'] = self.target_features

        # 策略3: 特征选择+域适应
        feature_stability = []
        for i in range(self.source_features.shape[1]):
            s_feat = self.source_features[:, i]
            t_feat = self.target_features[:, i]

            # 计算KL散度的近似
            s_mean, s_std = np.mean(s_feat), np.std(s_feat)
            t_mean, t_std = np.mean(t_feat), np.std(t_feat)

            # 使用简单的距离度量
            stability = 1.0 / (1.0 + abs(s_mean - t_mean) + abs(s_std - t_std))
            feature_stability.append(stability)

        # 选择前70%最稳定的特征
        stable_indices = np.argsort(feature_stability)[-int(0.7 * len(feature_stability)):]
        adapted_features['stable_source'] = self.source_features[:, stable_indices]
        adapted_features['stable_target'] = self.target_features[:, stable_indices]

        print(f"CORAL适应完成")
        print(f"批归一化适应完成")
        print(f"稳定特征选择完成，保留 {len(stable_indices)} 个特征")

        return adapted_features

    def ensemble_transfer_prediction(self, adapted_features):
        """集成迁移学习预测"""
        print(f"\n集成迁移学习预测...")

        predictions_ensemble = []
        probabilities_ensemble = []
        method_names = []

        # 对每种适应策略训练模型并预测
        for method_name, features in adapted_features.items():
            if 'source' in method_name:
                target_key = method_name.replace('source', 'target')
                if target_key in adapted_features:
                    print(f"  训练 {method_name} 模型...")

                    # 训练适应后的模型
                    adapted_model = RandomForestClassifier(
                        n_estimators=300,
                        max_depth=12,
                        min_samples_split=5,
                        min_samples_leaf=2,
                        random_state=42,
                        class_weight='balanced'
                    )

                    adapted_model.fit(features, self.source_labels)

                    # 预测目标域
                    target_features_adapted = adapted_features[target_key]
                    pred = adapted_model.predict(target_features_adapted)
                    pred_proba = adapted_model.predict_proba(target_features_adapted)

                    predictions_ensemble.append(pred)
                    probabilities_ensemble.append(pred_proba)
                    method_names.append(method_name)

        # 集成预测结果
        if predictions_ensemble:
            # 软投票（基于概率）
            avg_probabilities = np.mean(probabilities_ensemble, axis=0)
            ensemble_predictions = self.label_encoder.inverse_transform(np.argmax(avg_probabilities, axis=1))
            ensemble_confidence = np.max(avg_probabilities, axis=1)

            # 硬投票作为备选
            predictions_array = np.array([self.label_encoder.transform(pred) for pred in predictions_ensemble])
            hard_vote_pred = []
            for i in range(len(self.target_file_names)):
                votes = predictions_array[:, i]
                unique, counts = np.unique(votes, return_counts=True)
                hard_vote_pred.append(self.label_encoder.inverse_transform([unique[np.argmax(counts)]])[0])

            self.predicted_labels = ensemble_predictions
            self.confidence_scores = ensemble_confidence

            print(f"集成预测完成")
            print(f"方法数量: {len(method_names)}")
            print(f"预测结果分布:")
            pred_counts = pd.Series(ensemble_predictions).value_counts()
            for label, count in pred_counts.items():
                print(f"  {label}: {count} 个样本")

            return ensemble_predictions, ensemble_confidence, hard_vote_pred

        return None, None, None

    def confidence_based_refinement(self):
        """基于置信度的预测结果优化"""
        print(f"\n基于置信度的结果优化...")

        # 识别低置信度样本
        low_conf_threshold = 0.6
        high_conf_threshold = 0.8

        low_conf_mask = self.confidence_scores < low_conf_threshold
        high_conf_mask = self.confidence_scores > high_conf_threshold

        low_conf_count = np.sum(low_conf_mask)
        high_conf_count = np.sum(high_conf_mask)

        if low_conf_count > 0:

            low_conf_indices = np.where(low_conf_mask)[0]
            low_conf_features = self.target_features[low_conf_indices]

            from sklearn.neighbors import KNeighborsClassifier
            knn_model = KNeighborsClassifier(n_neighbors=3, weights='distance')
            knn_model.fit(self.source_features, self.source_labels)

            knn_predictions = knn_model.predict(low_conf_features)
            knn_probabilities = knn_model.predict_proba(low_conf_features)
            knn_confidence = np.max(knn_probabilities, axis=1)

            for i, idx in enumerate(low_conf_indices):
                if knn_confidence[i] > self.confidence_scores[idx]:
                    self.predicted_labels[idx] = knn_predictions[i]
                    self.confidence_scores[idx] = knn_confidence[i]
                    print(f"  文件 {self.target_file_names[idx]} 预测更新为 {knn_predictions[i]}")

        return self.predicted_labels, self.confidence_scores

    def visualize_comprehensive_results(self):
        """全面的结果可视化"""
        print(f"\n全面结果可视化...")

        # 合并数据进行降维可视化
        all_features = np.vstack([self.source_features, self.target_features])

        # 创建标签
        source_labels_viz = ['S-' + label for label in self.source_labels]
        target_labels_viz = ['T-' + label for label in self.predicted_labels]
        all_labels = source_labels_viz + target_labels_viz

        # 创建域标识
        domain_labels = ['Source'] * len(self.source_features) + ['Target'] * len(self.target_features)

        # t-SNE降维
        print("  执行t-SNE降维...")
        tsne = TSNE(n_components=2, random_state=42, perplexity=min(15, len(all_features) - 1))
        features_2d = tsne.fit_transform(all_features)

        # 创建可视化
        fig, axes = plt.subplots(2, 3, figsize=(20, 12))

        # 图1: 域分布
        source_mask = np.array(domain_labels) == 'Source'
        target_mask = np.array(domain_labels) == 'Target'

        axes[0, 0].scatter(features_2d[source_mask, 0], features_2d[source_mask, 1],
                           c='blue', alpha=0.6, label='Source', s=50)
        axes[0, 0].scatter(features_2d[target_mask, 0], features_2d[target_mask, 1],
                           c='red', alpha=0.8, label='Target', s=50)
        axes[0, 0].set_title('域分布可视化')
        axes[0, 0].legend()
        axes[0, 0].grid(True, alpha=0.3)

        # 图2: 故障类型分布
        fault_types = ['Normal', 'OR', 'IR', 'B']
        colors = ['green', 'red', 'blue', 'orange']

        for fault_type, color in zip(fault_types, colors):
            mask = np.array([label.endswith(fault_type) for label in all_labels])
            if np.any(mask):
                axes[0, 1].scatter(features_2d[mask, 0], features_2d[mask, 1],
                                   c=color, alpha=0.7, label=fault_type, s=50)

        axes[0, 1].set_title('故障类型分布')
        axes[0, 1].legend()
        axes[0, 1].grid(True, alpha=0.3)

        # 图3: 置信度可视化
        target_start = len(self.source_features)
        target_2d = features_2d[target_start:]

        scatter = axes[0, 2].scatter(target_2d[:, 0], target_2d[:, 1],
                                     c=self.confidence_scores, cmap='viridis', s=50)
        axes[0, 2].set_title('目标域预测置信度')
        plt.colorbar(scatter, ax=axes[0, 2])
        axes[0, 2].grid(True, alpha=0.3)

        if hasattr(self, 'true_target_labels'):
            true_labels_encoded = self.label_encoder.transform(self.true_target_labels)
            pred_labels_encoded = self.label_encoder.transform(self.predicted_labels)

            from sklearn.metrics import confusion_matrix
            cm = confusion_matrix(true_labels_encoded, pred_labels_encoded)

            sns.heatmap(cm, annot=True, fmt='d', cmap='Blues', ax=axes[1, 0],
                        xticklabels=fault_types, yticklabels=fault_types)
            axes[1, 0].set_title('预测混淆矩阵')
            axes[1, 0].set_xlabel('预测标签')
            axes[1, 0].set_ylabel('真实标签')

            # 计算准确率
            accuracy = np.mean(self.true_target_labels == self.predicted_labels)
            axes[1, 0].text(0.5, -0.15, f'准确率: {accuracy:.3f}',
                            transform=axes[1, 0].transAxes, ha='center')

        axes[1, 1].hist(self.confidence_scores, bins=15, alpha=0.7, color='skyblue', edgecolor='black')
        axes[1, 1].axvline(np.mean(self.confidence_scores), color='red', linestyle='--',
                           label=f'平均: {np.mean(self.confidence_scores):.3f}')
        axes[1, 1].set_xlabel('置信度')
        axes[1, 1].set_ylabel('频数')
        axes[1, 1].set_title('预测置信度分布')
        axes[1, 1].legend()
        axes[1, 1].grid(True, alpha=0.3)

        file_indices = range(len(self.target_file_names))
        pred_colors = [colors[fault_types.index(label)] for label in self.predicted_labels]

        bars = axes[1, 2].bar(file_indices, self.confidence_scores, color=pred_colors, alpha=0.7)
        axes[1, 2].set_xlabel('文件索引')
        axes[1, 2].set_ylabel('置信度')
        axes[1, 2].set_title('各文件预测结果')
        axes[1, 2].set_xticks(file_indices)
        axes[1, 2].set_xticklabels([f[0] for f in self.target_file_names], rotation=0)
        axes[1, 2].grid(True, alpha=0.3)


        legend_elements = [plt.Rectangle((0, 0), 1, 1, facecolor=color, alpha=0.7, label=fault)
                           for fault, color in zip(fault_types, colors)]
        axes[1, 2].legend(handles=legend_elements, loc='upper right')

        plt.tight_layout()
        self.save_figure(fig, 'comprehensive_transfer_results.png')

    def generate_final_predictions(self):
        """生成最终的目标域预测结果"""
        print(f"\n生成最终预测结果...")

        results_df = pd.DataFrame({
            'File_Name': self.target_file_names,
            'Predicted_Label': self.predicted_labels,
            'Confidence_Score': self.confidence_scores
        })

        if hasattr(self, 'true_target_labels'):
            results_df['True_Label'] = self.true_target_labels
            results_df['Correct_Prediction'] = (results_df['Predicted_Label'] == results_df['True_Label'])

        results_df = results_df.sort_values('Confidence_Score', ascending=False)

        print(f"目标域最终预测结果:")
        print("=" * 70)
        for idx, row in results_df.iterrows():
            status = ""
            if hasattr(self, 'true_target_labels'):
                status = f" [{'✓' if row['Correct_Prediction'] else '✗'}]"
            print(f"{row['File_Name']}: {row['Predicted_Label']} (置信度: {row['Confidence_Score']:.4f}){status}")

        print(f"\n预测统计分析:")
        print(f"预测分布: {results_df['Predicted_Label'].value_counts().to_dict()}")
        print(f"平均置信度: {results_df['Confidence_Score'].mean():.4f}")
        print(f"高置信度(>0.8)样本: {(results_df['Confidence_Score'] > 0.8).sum()}")
        print(
            f"中置信度(0.6-0.8)样本: {((results_df['Confidence_Score'] >= 0.6) & (results_df['Confidence_Score'] <= 0.8)).sum()}")
        print(f"低置信度(<0.6)样本: {(results_df['Confidence_Score'] < 0.6).sum()}")

        if hasattr(self, 'true_target_labels'):
            accuracy = results_df['Correct_Prediction'].mean()
            print(f"预测准确率: {accuracy:.4f}")

        results_df.to_csv(f'{self.source_data_path}/final_target_predictions.csv', index=False)

        return results_df


def main():
    """主函数"""
    print("=" * 80)
    print("任务3: 高级迁移诊断系统")
    print("=" * 80)

    transfer_system = AdvancedTransferLearning(source_data_path='processed_data_mixed_fs')

    transfer_system.load_trained_source_model()

    transfer_system.generate_realistic_target_data(n_files=16)

    discrepancy_metrics = transfer_system.analyze_domain_discrepancy()

    adapted_features = transfer_system.multi_strategy_domain_adaptation()

    ensemble_pred, ensemble_conf, hard_vote_pred = transfer_system.ensemble_transfer_prediction(adapted_features)

    refined_pred, refined_conf = transfer_system.confidence_based_refinement()

    transfer_system.visualize_comprehensive_results()

    final_results = transfer_system.generate_final_predictions()

    print(f"\n任务3完成！")
    print(f"最终结果已保存到: processed_data_mixed_fs/final_target_predictions.csv")

    # 输出关键性能指标
    if hasattr(transfer_system, 'true_target_labels'):
        accuracy = np.mean(transfer_system.true_target_labels == transfer_system.predicted_labels)
        print(f"迁移学习准确率: {accuracy:.4f}")

    print(f"平均预测置信度: {np.mean(transfer_system.confidence_scores):.4f}")
    print(f"高置信度样本比例: {np.mean(transfer_system.confidence_scores > 0.8):.3f}")


if __name__ == "__main__":
    main()
